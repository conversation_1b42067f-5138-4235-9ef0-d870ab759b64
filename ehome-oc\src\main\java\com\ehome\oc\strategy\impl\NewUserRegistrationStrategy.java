package com.ehome.oc.strategy.impl;

import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.domain.model.LoginUser;
import com.ehome.common.utils.SecurityUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.oc.domain.LoginContext;
import com.ehome.oc.domain.WxUser;
import com.ehome.oc.service.IWechatAuthService;
import com.ehome.oc.service.IWxUserService;
import com.ehome.oc.service.UserStatusService;
import com.ehome.oc.strategy.LoginStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 新用户注册策略 - 处理新用户注册流程
 * 
 * <AUTHOR>
 */
@Component
public class NewUserRegistrationStrategy implements LoginStrategy {

    private static final Logger logger = LoggerFactory.getLogger("sys-ds");

    @Autowired
    private IWechatAuthService wechatAuthService;

    @Autowired
    private IWxUserService wxUserService;

    @Autowired
    private UserStatusService userStatusService;

    @Override
    public boolean isApplicable(LoginContext context) {
        // 当用户不存在且有微信用户信息时适用
        return Boolean.FALSE.equals(context.getUserExists()) && context.hasWxUserInfo();
    }

    @Override
    public AjaxResult execute(LoginContext context) {
        try {
            logger.info("执行新用户注册策略，openid长度: {}",
                       context.getOpenid() != null ? context.getOpenid().length() : 0);

            // 检查是否有手机号授权码
            if (StringUtils.isEmpty(context.getPhoneCode())) {
                logger.info("新用户需要手机号授权");

                Map<String, Object> data = new HashMap<>();
                data.put("userExists", false);
                data.put("needPhoneAuth", true);
                data.put("message", "新用户需要手机号授权");
                data.put("checkResult", context.getCheckResult());

                // 更新上下文状态
                context.setNeedPhoneAuth(true);

                return AjaxResult.success("需要授权", data);
            }

            // 有手机号授权码，继续注册流程
            logger.info("新用户注册，开始处理手机号授权");

            // 处理新用户注册
            return processNewUserRegistration(context);

        } catch (Exception e) {
            logger.error("新用户注册失败: " + e.getMessage(), e);
            return AjaxResult.error("新用户注册失败: " + e.getMessage());
        }
    }

    /**
     * 处理新用户注册的具体逻辑
     *
     * @param context 登录上下文
     * @return 注册结果
     */
    private AjaxResult processNewUserRegistration(LoginContext context) {
        try {
            // 1. 解密手机号
            AjaxResult phoneResult = wechatAuthService.decryptPhoneNumber(null, context.getPhoneCode());
            Integer code = (Integer) phoneResult.get("code");
            if (code == null || code != 0) {
                String msg = (String) phoneResult.get("msg");
                logger.error("解密手机号失败: {}", msg);
                return AjaxResult.error("获取手机号失败: " + msg);
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> phoneData = (Map<String, Object>) phoneResult.get(AjaxResult.DATA_TAG);
            String phoneNumber = (String) phoneData.get("phoneNumber");

            if (StringUtils.isEmpty(phoneNumber)) {
                logger.error("解密手机号为空");
                return AjaxResult.error("获取手机号失败");
            }

            logger.info("新用户手机号解密成功");
            context.setPhoneNumber(phoneNumber);

            // 2. 检查用户身份
            Map<String, Object> identityResult = wxUserService.checkUserIdentityByPhone(phoneNumber);
            context.setIdentityResult(identityResult);

            boolean hasOwner = (Boolean) identityResult.get("hasOwnerIdentity");
            boolean hasProperty = (Boolean) identityResult.get("hasPropertyIdentity");

            // 如果同时具有两种身份，需要用户选择
            if (hasOwner && hasProperty) {
                logger.info("新用户具有多重身份，需要选择");

                Map<String, Object> data = new HashMap<>();
                data.put("needIdentitySelection", true);
                data.put("identityResult", identityResult);
                data.put("openid", context.getOpenid());
                data.put("unionid", context.getUnionid());
                data.put("sessionKey", context.getSessionKey());
                data.put("phoneNumber", phoneNumber);

                // 更新上下文状态
                context.setNeedIdentitySelection(true);

                return AjaxResult.success("需要选择登录身份", data);
            }

            // 3. 创建新用户并登录
            return createNewUserAndLogin(context, phoneNumber, hasOwner, hasProperty);

        } catch (Exception e) {
            logger.error("新用户注册处理失败: " + e.getMessage(), e);
            return AjaxResult.error("新用户注册失败: " + e.getMessage());
        }
    }

    /**
     * 创建新用户并登录
     *
     * @param context 登录上下文
     * @param phoneNumber 手机号
     * @param hasOwner 是否有业主身份
     * @param hasProperty 是否有物业身份
     * @return 登录结果
     */
    private AjaxResult createNewUserAndLogin(LoginContext context, String phoneNumber, boolean hasOwner, boolean hasProperty) {
        try {
            // 创建新的微信用户
            WxUser newUser = new WxUser();
            newUser.setOpenId(context.getOpenid());
            newUser.setUnionId(context.getUnionid());
            newUser.setSessionKey(context.getSessionKey());
            newUser.setMobile(phoneNumber);
            newUser.setStatus("0"); // 正常状态
            newUser.setIsFirstLogin(true);

            // 设置用户角色
            String roles = "";
            if (hasOwner) roles = "owner";
            if (hasProperty) roles = roles.isEmpty() ? "property" : roles + ",property";
            newUser.setUserRoles(roles);

            // 如果有业主身份，设置业主相关信息
            if (hasOwner && context.getIdentityResult() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> ownerUser = (Map<String, Object>) context.getIdentityResult().get("ownerUser");
                if (ownerUser != null) {
                    newUser.setOwnerId((String) ownerUser.get("ownerId"));
                    newUser.setCommunityId((String) ownerUser.get("communityId"));
                }
            }

            // 保存新用户
            int result = wxUserService.insertWxUser(newUser);
            if (result <= 0) {
                logger.error("创建新用户失败");
                return AjaxResult.error("用户注册失败");
            }

            logger.info("新用户创建成功，用户ID: {}", newUser.getUserId());
            context.setWxUser(newUser);

            // 构建登录用户信息
            LoginUser loginUser = new LoginUser();
            loginUser.setOpenId(newUser.getOpenId());
            loginUser.setUserId(newUser.getUserId());
            loginUser.setNickname(newUser.getNickName());
            loginUser.setMobile(newUser.getMobile());
            loginUser.setOwnerId(newUser.getOwnerId());
            loginUser.setUserType(hasProperty && !hasOwner ? "2" : "1"); // 优先业主身份

            // 使用统一的用户状态服务获取用户状态数据
            Map<String, Object> statusData = userStatusService.buildUserStatusData(loginUser, false);

            // 检查房屋认证状态
            boolean isHouseAuth = (Boolean) statusData.getOrDefault("isHouseAuth", false);
            String authStatus = isHouseAuth ? "verified" : (hasOwner ? "pending" : "none");

            // 构建返回数据
            Map<String, Object> data = new HashMap<>(statusData);
            data.put("userInfo", newUser);
            data.put("hasBindPhone", true);
            data.put("authStatus", authStatus);
            data.put("needPhoneAuth", false);
            data.put("needHouseAuth", !isHouseAuth);
            data.put("isFirstLogin", true);
            data.put("userExists", false); // 新用户标识

            // 只有房屋认证成功或物业用户才生成token
            if (isHouseAuth || hasProperty) {
                String token = SecurityUtils.createToken(loginUser);
                data.put("token", token);
                logger.info("新用户注册成功，用户ID: {}, 房屋认证: {}", newUser.getUserId(), isHouseAuth);
            } else {
                data.put("token", "");
                logger.info("新用户房屋认证未完成，不生成token，用户ID: {}", newUser.getUserId());
            }

            return AjaxResult.success("注册成功", data);

        } catch (Exception e) {
            logger.error("创建新用户并登录失败: " + e.getMessage(), e);
            return AjaxResult.error("用户注册失败: " + e.getMessage());
        }
    }

    @Override
    public int getPriority() {
        return 30; // 中等优先级
    }

    @Override
    public String getStrategyName() {
        return "NewUserRegistrationStrategy";
    }
}
